<variant
    name="release"
    package="com.c2hapmll.tools"
    minSdkVersion="26"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.6.0;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\4026572f5835045fbba776502ac5cc6e\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.c2hapmll.tools"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-4\4026572f5835045fbba776502ac5cc6e\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
