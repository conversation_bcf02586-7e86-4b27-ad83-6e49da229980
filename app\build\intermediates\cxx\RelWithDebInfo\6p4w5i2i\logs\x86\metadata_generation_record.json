[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: x86", "file_": "D:\\work\\AndroidStudioProjects\\tools\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\work\\AndroidStudioProjects\\tools\\app\\.cxx\\RelWithDebInfo\\6p4w5i2i\\x86\\android_gradle_build.json' was up-to-date", "file_": "D:\\work\\AndroidStudioProjects\\tools\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\work\\AndroidStudioProjects\\tools\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]