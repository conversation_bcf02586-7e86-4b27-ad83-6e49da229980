1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.c2hapmll.tools"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- 蓝牙权限 -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:7:5-74
13-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
15-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:9:5-73
15-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:9:22-70
16    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
16-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:10:22-73
17    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
17-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:11:5-78
17-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:11:22-75
18
19    <uses-feature
19-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:13:5-15:35
20        android:name="android.hardware.bluetooth"
20-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:14:9-50
21        android:required="true" />
21-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:15:9-32
22
23    <permission
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
24        android:name="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
28
29    <application
29-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:17:5-38:19
30        android:allowBackup="true"
30-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:18:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f2cd572c2f5674e7ed4cfd990fc7ac0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:19:9-65
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:20:9-54
35        android:icon="@mipmap/ic_launcher"
35-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:21:9-43
36        android:label="@string/app_name"
36-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:22:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:23:9-54
38        android:supportsRtl="true"
38-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:24:9-35
39        android:theme="@style/Theme.Tools" >
39-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:25:9-43
40        <activity
40-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:27:9-34:20
41            android:name="com.c2hapmll.tools.MainActivity"
41-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:28:13-41
42            android:exported="true" >
42-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:29:13-36
43            <intent-filter>
43-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:30:13-33:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:31:17-69
44-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:31:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:32:17-77
46-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:32:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:35:9-37:40
50            android:name="com.c2hapmll.tools.DeveloperActivity"
50-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:36:13-46
51            android:exported="false" />
51-->D:\work\AndroidStudioProjects\tools\app\src\main\AndroidManifest.xml:37:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.c2hapmll.tools.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\940ddfe6b0cd5e755c771e8b38d57035\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\deffca864b36bed43b78ebb16759317c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\deffca864b36bed43b78ebb16759317c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\deffca864b36bed43b78ebb16759317c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\91a0c5d929da08d998dd5e2b8f19469e\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
