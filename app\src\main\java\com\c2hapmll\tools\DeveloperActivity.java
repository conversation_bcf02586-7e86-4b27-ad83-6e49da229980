package com.c2hapmll.tools;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanResult;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;

public class DeveloperActivity extends AppCompatActivity {
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;
    
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothLeScanner bluetoothLeScanner;
    private Button btnScanBluetooth, btnStopScan;
    private RecyclerView rvBluetoothDevices;
    private BluetoothDeviceAdapter deviceAdapter;
    private List<BluetoothDeviceInfo> deviceList;
    private boolean isScanning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_developer);

        initViews();
        initBluetooth();
        setupRecyclerView();
        setupClickListeners();
    }

    private void initViews() {
        ImageButton btnBack = findViewById(R.id.btn_back);
        btnScanBluetooth = findViewById(R.id.btn_scan_bluetooth);
        btnStopScan = findViewById(R.id.btn_stop_scan);
        rvBluetoothDevices = findViewById(R.id.rv_bluetooth_devices);

        btnBack.setOnClickListener(v -> finish());
    }

    private void initBluetooth() {
        BluetoothManager bluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();
        
        if (bluetoothAdapter != null) {
            bluetoothLeScanner = bluetoothAdapter.getBluetoothLeScanner();
        }
    }

    private void setupRecyclerView() {
        deviceList = new ArrayList<>();
        deviceAdapter = new BluetoothDeviceAdapter(deviceList, this::connectToDevice);
        rvBluetoothDevices.setLayoutManager(new LinearLayoutManager(this));
        rvBluetoothDevices.setAdapter(deviceAdapter);
    }

    private void setupClickListeners() {
        btnScanBluetooth.setOnClickListener(v -> startBluetoothScan());
        btnStopScan.setOnClickListener(v -> stopBluetoothScan());
    }

    private void startBluetoothScan() {
        if (!checkBluetoothPermissions()) {
            requestBluetoothPermissions();
            return;
        }

        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            Toast.makeText(this, "请先开启蓝牙", Toast.LENGTH_SHORT).show();
            return;
        }

        if (isScanning) {
            Toast.makeText(this, "正在扫描中...", Toast.LENGTH_SHORT).show();
            return;
        }

        deviceList.clear();
        deviceAdapter.notifyDataSetChanged();
        
        // 注册经典蓝牙扫描接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        registerReceiver(bluetoothReceiver, filter);

        // 开始经典蓝牙扫描
        bluetoothAdapter.startDiscovery();
        
        // 开始BLE扫描
        if (bluetoothLeScanner != null) {
            bluetoothLeScanner.startScan(leScanCallback);
        }

        isScanning = true;
        btnScanBluetooth.setText("扫描中...");
        btnScanBluetooth.setEnabled(false);
        Toast.makeText(this, "开始扫描蓝牙设备", Toast.LENGTH_SHORT).show();
    }

    private void stopBluetoothScan() {
        if (!isScanning) return;

        if (bluetoothAdapter != null) {
            bluetoothAdapter.cancelDiscovery();
        }
        
        if (bluetoothLeScanner != null) {
            bluetoothLeScanner.stopScan(leScanCallback);
        }

        try {
            unregisterReceiver(bluetoothReceiver);
        } catch (IllegalArgumentException e) {
            // 接收器未注册
        }

        isScanning = false;
        btnScanBluetooth.setText("扫描蓝牙设备");
        btnScanBluetooth.setEnabled(true);
        Toast.makeText(this, "停止扫描", Toast.LENGTH_SHORT).show();
    }

    private final BroadcastReceiver bluetoothReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                int rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);
                addDevice(device, rssi);
            }
        }
    };

    private final ScanCallback leScanCallback = new ScanCallback() {
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            BluetoothDevice device = result.getDevice();
            int rssi = result.getRssi();
            addDevice(device, rssi);
        }
    };

    private void addDevice(BluetoothDevice device, int rssi) {
        if (device == null) return;

        // 检查设备是否已存在
        for (BluetoothDeviceInfo info : deviceList) {
            if (info.getAddress().equals(device.getAddress())) {
                return;
            }
        }

        String name = "未知设备";
        if (checkBluetoothPermissions() && device.getName() != null) {
            name = device.getName();
        }

        BluetoothDeviceInfo deviceInfo = new BluetoothDeviceInfo(
            name, device.getAddress(), rssi, device
        );
        
        deviceList.add(deviceInfo);
        runOnUiThread(() -> deviceAdapter.notifyItemInserted(deviceList.size() - 1));
    }

    private void connectToDevice(BluetoothDeviceInfo deviceInfo) {
        Toast.makeText(this, "尝试连接到: " + deviceInfo.getName(), Toast.LENGTH_SHORT).show();
        // 这里可以添加具体的连接逻辑
    }

    private boolean checkBluetoothPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    private void requestBluetoothPermissions() {
        ActivityCompat.requestPermissions(this, new String[]{
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.ACCESS_FINE_LOCATION
        }, REQUEST_BLUETOOTH_PERMISSIONS);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopBluetoothScan();
    }
}