<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f8f8f8">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#ffffff"
        android:elevation="2dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="开发者工具"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 蓝牙功能卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="蓝牙设备管理"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/btn_scan_bluetooth"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="扫描蓝牙设备"
                        android:textColor="#ffffff"
                        android:backgroundTint="#2196F3"
                        android:layout_marginBottom="8dp" />

                    <Button
                        android:id="@+id/btn_stop_scan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="停止扫描"
                        android:textColor="#ffffff"
                        android:backgroundTint="#FF9800"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="发现的设备："
                        android:textColor="#666666"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_bluetooth_devices"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:background="#f0f0f0" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>
</LinearLayout>