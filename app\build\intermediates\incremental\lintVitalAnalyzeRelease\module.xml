<lint-module
    format="1"
    dir="D:\work\AndroidStudioProjects\tools\app"
    name=":app"
    type="APP"
    maven="tools:app:unspecified"
    agpVersion="8.6.0"
    buildFolder="build"
    bootClassPath="D:\software\install\Android\Sdk\platforms\android-34\android.jar;D:\software\install\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
