package com.c2hapmll.tools;

public class MapInfo {
    public long start;
    public long end;
    public int perms;
    public boolean isPrivate;
    public long offset;
    public long device;
    public long inode;
    public String path;

    public MapInfo(long start, long end, int perms, boolean isPrivate, long offset, 
                  long device, long inode, String path) {
        this.start = start;
        this.end = end;
        this.perms = perms;
        this.isPrivate = isPrivate;
        this.offset = offset;
        this.device = device;
        this.inode = inode;
        this.path = path;
    }
} 