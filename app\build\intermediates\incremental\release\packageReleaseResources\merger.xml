<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\src\main\res"><file name="ic_launcher_background" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_transparent" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\layout\activity_transparent.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\work\AndroidStudioProjects\tools\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\work\AndroidStudioProjects\tools\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">tools</string></file><file path="D:\work\AndroidStudioProjects\tools\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Tools" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">#ff6699</item>
        <item name="colorPrimaryDark">#ff5588</item>
        <item name="colorAccent">#ff6699</item>
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:statusBarColor">#ffffff</item>
        <item name="android:windowLightStatusBar">true</item>
    </style><style name="Theme.Tools" parent="Base.Theme.Tools"/></file><file path="D:\work\AndroidStudioProjects\tools\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Tools" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="activity_developer" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\layout\activity_developer.xml" qualifiers="" type="layout"/><file name="item_bluetooth_device" path="D:\work\AndroidStudioProjects\tools\app\src\main\res\layout\item_bluetooth_device.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\AndroidStudioProjects\tools\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>