<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_device_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设备名称"
        android:textColor="#333333"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_device_address"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="MAC地址"
        android:textColor="#666666"
        android:textSize="14sp"
        android:layout_marginTop="4dp" />

    <TextView
        android:id="@+id/tv_device_rssi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="信号强度"
        android:textColor="#999999"
        android:textSize="12sp"
        android:layout_marginTop="2dp" />

</LinearLayout>