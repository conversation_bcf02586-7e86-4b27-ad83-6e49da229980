{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\.cxx\\RelWithDebInfo\\6p4w5i2i\\arm64-v8a", "soFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6p4w5i2i\\obj\\arm64-v8a", "soRepublishFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cmake\\release\\obj\\arm64-v8a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [""], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\.cxx", "intermediatesBaseFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates", "intermediatesFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "D:\\work\\AndroidStudioProjects\\tools\\app", "moduleBuildFile": "D:\\work\\AndroidStudioProjects\\tools\\app\\build.gradle", "makeFile": "D:\\work\\AndroidStudioProjects\\tools\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125", "ndkFolderBeforeSymLinking": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\software\\install\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\work\\AndroidStudioProjects\\tools", "sdkFolder": "D:\\software\\install\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\software\\install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\.cxx\\RelWithDebInfo\\6p4w5i2i\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6p4w5i2i14422m134z5f4g5oz5t6a6y4b5a4d4vs3xt42f2z2j1c1g5j3262", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.0.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-B$PROJECT/app/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HD:\\work\\AndroidStudioProjects\\tools\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=D:\\software\\install\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\software\\install\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6p4w5i2i\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6p4w5i2i\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-BD:\\work\\AndroidStudioProjects\\tools\\app\\.cxx\\RelWithDebInfo\\6p4w5i2i\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "D:\\work\\AndroidStudioProjects\\tools\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6p4w5i2i"}