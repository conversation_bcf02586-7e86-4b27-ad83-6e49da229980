<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f8f8f8">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#ffffff"
        android:elevation="2dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="安全检测"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 安全检测卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设备安全检测"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/btn_root_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="内存注入检测"
                        android:textColor="#ffffff"
                        android:backgroundTint="#ff6699"
                        android:layout_marginBottom="8dp" />

                    <Button
                        android:id="@+id/btn_root_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Root文件检测"
                        android:textColor="#ffffff"
                        android:backgroundTint="#ff6699" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 安全提示卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="安全提示"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Root设备可能会导致您的个人信息泄露，建议使用未Root的设备。"
                        android:textColor="#666666"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</LinearLayout> 
