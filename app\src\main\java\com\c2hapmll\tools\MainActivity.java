package com.c2hapmll.tools;

import android.os.Bundle;
import android.widget.Button;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    private Button btnRoot1;
    private Button btnRoot2;
    private NativeLib nativeLib;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        nativeLib = new NativeLib();
        btnRoot1 = findViewById(R.id.btn_root_1);
        btnRoot2 = findViewById(R.id.btn_root_2);

        // 启动时运行内存注入检测
        new Thread(() -> {
            boolean injectionResult = nativeLib.detectInjection();
            runOnUiThread(() -> {
                btnRoot1.setText(injectionResult ? "⚠️ 检测到内存注入" : "✓ 内存环境安全");
                if (injectionResult) {
                    btnRoot1.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
                } else {
                    btnRoot1.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
                }
            });
        }).start();
        
        // 启动时运行常见路径检测
        new Thread(() -> {
            boolean pathResult = nativeLib.checkCommonPaths();
            runOnUiThread(() -> {
                btnRoot2.setText(pathResult ? "⚠️ 检测到Root文件" : "✓ 未发现Root文件");
                if (pathResult) {
                    btnRoot2.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
                } else {
                    btnRoot2.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
                }
            });
        }).start();

        // 设置第一个按钮的点击事件 - 重新检测内存注入
        btnRoot1.setOnClickListener(v -> {
            btnRoot1.setText("检测中...");
            btnRoot1.setBackgroundTintList(getColorStateList(android.R.color.darker_gray));
            
            new Thread(() -> {
                boolean hasInjection = nativeLib.detectInjection();
                runOnUiThread(() -> {
                    btnRoot1.setText(hasInjection ? "⚠️ 检测到内存注入" : "✓ 内存环境安全");
                    if (hasInjection) {
                        btnRoot1.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
                        Toast.makeText(this, "警告：检测到可疑内存注入", Toast.LENGTH_LONG).show();
                    } else {
                        btnRoot1.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
                        Toast.makeText(this, "您的设备内存环境安全", Toast.LENGTH_SHORT).show();
                    }
                });
            }).start();
        });
        
        // 设置第二个按钮点击事件 - 检测常见路径
        btnRoot2.setOnClickListener(v -> {
            btnRoot2.setText("检测中...");
            btnRoot2.setBackgroundTintList(getColorStateList(android.R.color.darker_gray));
            
            new Thread(() -> {
                boolean exists = nativeLib.checkCommonPaths();
                runOnUiThread(() -> {
                    btnRoot2.setText(exists ? "⚠️ 检测到Root文件" : "✓ 未发现Root文件");
                    if (exists) {
                        btnRoot2.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
                        Toast.makeText(this, "警告：检测到Root相关文件", Toast.LENGTH_SHORT).show();
                    } else {
                        btnRoot2.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
                        Toast.makeText(this, "未检测到Root相关文件", Toast.LENGTH_SHORT).show();
                    }
                });
            }).start();
        });

        // 添加开发者工具按钮
        Button btnDeveloper = new Button(this);
        btnDeveloper.setText("开发者工具");
        btnDeveloper.setOnClickListener(v -> {
            Intent intent = new Intent(this, DeveloperActivity.class);
            startActivity(intent);
        });

        // 找到主布局并添加按钮（需要修改布局或通过代码添加）
        // 这里假设你有一个名为mainLayout的布局
        // LinearLayout mainLayout = findViewById(R.id.mainLayout);
        // mainLayout.addView(btnDeveloper);
    }
} 
