http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_developer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_bluetooth_device.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_transparent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:white,0,V400030061,**********,;"#FFFFFFFF";black,0,V400020037,290002005c,;"#FF000000";+drawable:ic_launcher_foreground,1,F;ic_launcher_background,2,F;+id:rv_bluetooth_devices,3,F;tv_device_name,4,F;btn_stop_scan,3,F;btn_root_2,5,F;btn_scan_bluetooth,3,F;btn_back,3,F;main,6,F;tv_device_address,4,F;tv_device_rssi,4,F;btn_root_1,5,F;+layout:activity_main,5,F;activity_developer,3,F;item_bluetooth_device,4,F;activity_transparent,6,F;+mipmap:ic_launcher_round,7,F;ic_launcher_round,8,F;ic_launcher_round,9,F;ic_launcher_round,10,F;ic_launcher_round,11,F;ic_launcher_round,12,F;ic_launcher_foreground,13,F;ic_launcher_foreground,14,F;ic_launcher_foreground,15,F;ic_launcher_foreground,16,F;ic_launcher_foreground,17,F;ic_launcher,18,F;ic_launcher,19,F;ic_launcher,20,F;ic_launcher,21,F;ic_launcher,22,F;ic_launcher,23,F;+string:app_name,24,V400010010,2a00010036,;"tools";+style:Base.Theme.Tools,25,V400020064,c000a0249,;DTheme.Material3.DayNight.NoActionBar,colorPrimary:#ff6699,colorPrimaryDark:#ff5588,colorAccent:#ff6699,android\:windowBackground:@android\:color/white,android\:statusBarColor:#ffffff,android\:windowLightStatusBar:true,;Base.Theme.Tools,26,V400020064,c00050138,;DTheme.Material3.DayNight.NoActionBar,;Theme.Tools,25,V4000c024f,3a000c0285,;DBase.Theme.Tools,;+xml:data_extraction_rules,27,F;backup_rules,28,F;