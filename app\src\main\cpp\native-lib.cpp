#include <jni.h>
#include <string>
#include <android/log.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <regex>
#include <vector>
#include <fstream>
#include <sstream>
#include <sys/auxv.h>

#define LOG_TAG "NativeLib"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

struct MapInfo {
    unsigned long start;
    unsigned long end;
    int perms;
    bool isPrivate;
    unsigned long offset;
    unsigned long dev;
    unsigned long inode;
    std::string path;
};

// 打印单个内存映射信息
void printMapInfo(const MapInfo& info) {
    char perms[5] = "----";
    if (info.perms & 0x4) perms[0] = 'r';
    if (info.perms & 0x2) perms[1] = 'w';
    if (info.perms & 0x1) perms[2] = 'x';
    perms[3] = info.isPrivate ? 'p' : 's';
    
    LOGI("内存映射: %lx-%lx %s %lx %lx:%lx %lu %s",
        info.start, info.end, perms, info.offset,
        (info.dev >> 8) & 0xFF, info.dev & 0xFF,
        info.inode, info.path.c_str());
}

std::vector<MapInfo> scanMaps() {
    std::vector<MapInfo> infoList;
    std::ifstream mapsFile("/proc/self/maps");
    std::string line;
    std::regex pattern("^([0-9a-f]+)-([0-9a-f]+)\\s+([rwxps-]{4})\\s+([0-9a-f]+)\\s+([0-9a-f]+):([0-9a-f]+)\\s+([0-9]+)\\s+(.*)$");

    LOGI("开始扫描内存映射...");
    while (std::getline(mapsFile, line)) {
        std::smatch matches;
        if (std::regex_match(line, matches, pattern)) {
            MapInfo info;
            info.start = std::stoul(matches[1].str(), nullptr, 16);
            info.end = std::stoul(matches[2].str(), nullptr, 16);
            std::string perms = matches[3].str();
            info.offset = std::stoul(matches[4].str(), nullptr, 16);
            unsigned long devMajor = std::stoul(matches[5].str(), nullptr, 16);
            unsigned long devMinor = std::stoul(matches[6].str(), nullptr, 16);
            info.inode = std::stoul(matches[7].str());
            info.path = matches[8].str();

            info.perms = 0;
            if (perms[0] == 'r') info.perms |= 0x4;
            if (perms[1] == 'w') info.perms |= 0x2;
            if (perms[2] == 'x') info.perms |= 0x1;
            info.isPrivate = perms[3] == 'p';
            info.dev = (devMajor << 8) | devMinor;

            infoList.push_back(info);
            printMapInfo(info);
        }
    }
    LOGI("内存映射扫描完成，共发现 %zu 个映射", infoList.size());
    return infoList;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_c2hapmll_tools_NativeLib_detectInjection(JNIEnv* env, jobject /* this */) {
    int jitCacheCount = 0;
    int jitZygoteCacheCount = 0;
    unsigned long vdsoStart = (unsigned long)getauxval(AT_SYSINFO_EHDR);
    std::vector<MapInfo> execMaps;

    LOGI("vdso起始地址: %lx", vdsoStart);

    // 第一步：收集所有可执行内存区域
    for (const auto& info : scanMaps()) {
        if ((info.perms & 0x1) != 0) { // PROT_EXEC
            execMaps.push_back(info);
        }
    }
    LOGI("发现 %zu 个可执行内存区域", execMaps.size());

    // 第二步：检查每个可执行内存区域
    for (const auto& info : execMaps) {
        // 2. 检查匿名可执行内存块
        if (info.path.find("/dev/zero") == 0) {
            LOGI("检测到可疑内存映射: 匿名可执行内存 %s", info.path.c_str());
            return JNI_TRUE;
        }
    }

    LOGI("内存注入检测完成，未发现异常");
    return JNI_FALSE;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_c2hapmll_tools_NativeLib_checkCommonPaths(JNIEnv* env, jobject /* this */) {
    const char* paths[] = {
        "/system_ext/bin/magisk",
        "/system_ext/bin/magiskpolicy",
        "/debug_ramdisk/.magisk/",
        "/debug_ramdisk/magisk",
        "/debug_ramdisk/magisk32",
        "/debug_ramdisk/magiskinit",
        "/debug_ramdisk/magiskpolicy",
        "/debug_ramdisk/su",
        "/data/adb/modules",
        "/data/adb/post-fs-data.d",
        "/data/adb/service.d"
    };

    LOGI("开始检查常见路径...");
    for (const char* path : paths) {
        if (access(path, F_OK) == 0) {
            LOGI("检测到可疑文件: %s", path);
            return JNI_TRUE;
        }
    }
    LOGI("常见路径检查完成，未发现异常");
    return JNI_FALSE;
}

