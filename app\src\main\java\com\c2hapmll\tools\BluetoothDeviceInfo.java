package com.c2hapmll.tools;

import android.bluetooth.BluetoothDevice;

public class BluetoothDeviceInfo {
    private String name;
    private String address;
    private int rssi;
    private BluetoothDevice device;

    public BluetoothDeviceInfo(String name, String address, int rssi, BluetoothDevice device) {
        this.name = name;
        this.address = address;
        this.rssi = rssi;
        this.device = device;
    }

    public String getName() { return name; }
    public String getAddress() { return address; }
    public int getRssi() { return rssi; }
    public BluetoothDevice getDevice() { return device; }
}