[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\layout_activity_frida.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\layout\\activity_frida.xml"}, {"merged": "com.c2hapmll.tools.app-release-31:/layout_item_bluetooth_device.xml.flat", "source": "com.c2hapmll.tools.app-main-32:/layout/item_bluetooth_device.xml"}, {"merged": "com.c2hapmll.tools.app-release-31:/layout_activity_developer.xml.flat", "source": "com.c2hapmll.tools.app-main-32:/layout/activity_developer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\layout_activity_root_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\layout\\activity_root_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\layout_activity_algorithm.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\layout\\activity_algorithm.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\layout_activity_transparent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\layout\\activity_transparent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-release-31:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.c2hapmll.tools.app-main-32:\\mipmap-anydpi\\ic_launcher.xml"}]